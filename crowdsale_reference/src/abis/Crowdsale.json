[{"inputs": [{"internalType": "contract Token", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_price", "type": "uint256"}, {"internalType": "uint256", "name": "_maxTokens", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "buyer", "type": "address"}], "name": "Buy", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "tokensSold", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ethRaised", "type": "uint256"}], "name": "Finalize", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "buyTokens", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "finalize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_price", "type": "uint256"}], "name": "setPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract Token", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokensSold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]
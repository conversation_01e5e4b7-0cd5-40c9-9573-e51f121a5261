{"name": "crowdsale", "version": "1.0.0", "description": "", "dependencies": {"@openzeppelin/contracts": "^5.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "bootstrap": "^5.3.7", "ethers": "^6.15.0", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.0-placeholder-for-preset-env.2", "@nomicfoundation/hardhat-toolbox": "^6.0.0", "hardhat": "^2.25.0"}}
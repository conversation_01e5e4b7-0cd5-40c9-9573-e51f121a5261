# Blockchain Configuration
INFURA_API_KEY=your_infura_api_key
PRIVATE_KEY=your_wallet_private_key
ETHERSCAN_API_KEY=your_etherscan_api_key

# USPTO API Configuration
VITE_USPTO_API_KEY=your_uspto_api_key

# AI Services Configuration (Choose one or more)
# OpenAI (Recommended - Most powerful)
VITE_OPENAI_API_KEY=your_openai_api_key

# Google Gemini (Cost-effective alternative)
VITE_GEMINI_API_KEY=your_gemini_api_key

# Hugging Face (Free alternative)
VITE_HUGGINGFACE_API_KEY=your_huggingface_api_key

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Crypto Payment Configuration
VITE_PAYMENT_RECIPIENT_ADDRESS=0xYourWalletAddressHere
VITE_PAYMENT_CONTRACT_ADDRESS=0xYourContractAddressHere

# Backend Configuration
VITE_BACKEND_URL=http://localhost:3001

# Development Configuration
VITE_ENABLE_MOCK_DATA=false

# Development Configuration
VITE_ENABLE_MOCK_DATA=false
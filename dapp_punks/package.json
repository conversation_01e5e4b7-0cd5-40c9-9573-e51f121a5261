{"_comment": "Updated dependencies for security", "name": "dapp_punks", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "bootstrap": "^5.3.7", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-countdown": "^2.3.5", "react-dom": "^19.1.0", "web-vitals": "^5.0.3"}, "scripts": {"start": "vite", "build": "vite build", "test": "vitest", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "browserify-zlib": "^0.2.0", "hardhat": "^2.25.0", "process": "^0.11.10", "stream-browserify": "^3.0.0", "util": "^0.12.5", "vite": "^7.0.3", "vitest": "^3.2.4"}}
{"name": "dapp_punks", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "bootstrap": "^5.3.2", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-countdown": "^2.3.5", "react-dom": "^18.2.0", "react-scripts": "^5.0.1", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.19.2"}}